<?php
#region region DOCS

/** @var Activo|null $activo */
/** @var array $imagenes */
/** @var string $error_display */
/** @var string $error_text */
/** @var Inventario|null $inventario_estado */

/** @var Contenedor[] $contenedores */

use App\classes\Activo;
use App\classes\Inventario;
use App\classes\Contenedor;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Información del Activo</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- Custom styles for scanactivo -->
	<style>
        .image-container {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            transition: transform 0.2s ease;
        }

        .image-container:hover {
            transform: scale(1.02);
        }

        .image-container img {
            transition: opacity 0.2s ease;
        }

        .image-container:hover img {
            opacity: 0.9;
        }

        .form-control-static {
            padding: 8px 0;
            margin-bottom: 0;
            min-height: 20px;
            word-wrap: break-word;
        }

        .form-label {
            margin-bottom: 5px;
            color: #212529;
            font-weight: 600;
        }
	</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Información del Activo</h4>
				<p class="mb-0 text-muted">Detalles del activo escaneado</p>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php if ($error_display === 'show'): ?>
			<div class="alert alert-danger alert-dismissible fade show">
				<strong>Error!</strong> <?php echo $error_text; ?>
				<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
			</div>
		<?php endif; ?>

		<?php if (!empty($success_text)): ?>
			<div class="alert alert-success alert-dismissible fade show">
				<strong>Success!</strong> <?php echo $success_text; ?>
				<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
			</div>
		<?php endif; ?>


		<?php if ($activo): ?>
		<?php #region region PANEL ACTIVO ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Información del Activo
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="panel-body">
				<!-- Basic Information -->
				<div class="row">
					<div class="col-md-12">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Descripción:</strong></label>
							<p class="form-control-static"><?php echo htmlspecialchars($activo->getDescripcion() ?? ''); ?></p>
						</div>
					</div>
				</div>

				<!-- Brand and Model -->
				<div class="row">
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Marca:</strong></label>
							<p class="form-control-static"><?php echo htmlspecialchars($activo->getMarca() ?? 'No especificada'); ?></p>
						</div>
					</div>
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Modelo:</strong></label>
							<p class="form-control-static"><?php echo htmlspecialchars($activo->getModelo() ?? 'No especificado'); ?></p>
						</div>
					</div>
				</div>

				<!-- Serial Number and Acquisition Date -->
				<div class="row">
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Número de Serie:</strong></label>
							<p class="form-control-static"><?php echo htmlspecialchars($activo->getNumeroSerie() ?? 'No especificado'); ?></p>
						</div>
					</div>
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Fecha de Adquisición:</strong></label>
							<p class="form-control-static">
								<?php
								$fechaAdquisicion = $activo->getFechaAdquisicion();
								if ($fechaAdquisicion && $fechaAdquisicion !== '0000-00-00' && $fechaAdquisicion !== '') {
									echo htmlspecialchars($fechaAdquisicion);
								} else {
									echo 'N/A';
								}
								?>
							</p>
						</div>
					</div>


				</div>

				<!-- Asset Value -->
				<div class="row">
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Valor del Activo:</strong></label>
							<p class="form-control-static">
								<?php echo $activo->getValorActivo() ? '$' . number_format($activo->getValorActivo(), 0, '', '.') : 'No especificado'; ?>
							</p>
						</div>
					</div>
				</div>

				<!-- Characteristics -->
				<div class="row">
					<div class="col-md-12">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Características:</strong></label>
							<p class="form-control-static"><?php echo nl2br(htmlspecialchars($activo->getCaracteristicas() ?? 'No especificadas')); ?></p>
						</div>
					</div>
				</div>

				<!-- Comments -->
				<div class="row">
					<div class="col-md-12">
						<div class="mb-3">
							<label class="form-label text-info"><strong>Comentarios:</strong></label>
							<p class="form-control-static"><?php echo nl2br(htmlspecialchars($activo->getComentarios() ?? 'Sin comentarios')); ?></p>
						</div>
					</div>
				</div>

				<!-- Inventory Status (moved and styled) -->
				<div class="bg-info bg-opacity-10 border-start border-info border-4 rounded p-3 mt-3">
					<div class="row mb-2">
						<div class="col-12 d-flex align-items-center">
							<h3 class="mb-2 text-white-500">Estado de Inventario</h3>
						</div>
					</div>
					<?php if (!empty($inventario_estado)): ?>
						<div class="row">
							<div class="col-12 col-sm-6 col-md-4">
								<div class="mb-3">
									<label class="form-label text-info"><strong>Contenedor asignado:</strong></label>
									<p class="form-control-static"><?php echo htmlspecialchars($inventario_estado->getContenedor_descripcion() ?? 'N/A'); ?></p>
								</div>
							</div>
							<div class="col-12 col-sm-6 col-md-4">
								<div class="mb-3">
									<label class="form-label text-info"><strong>Estado:</strong></label>
									<p class="form-control-static">
										<?php if ((int)$inventario_estado->getEn_contenedor() === 1): ?>
											<span class="badge bg-success">No prestado</span>
										<?php else: ?>
											<span class="badge bg-danger">Prestado</span>
										<?php endif; ?>
									</p>
								</div>
							</div>
							<div class="col-12 col-sm-12 col-md-4">
								<div class="mb-3">
									<label class="form-label text-info"><strong>Trabajador asignado:</strong></label>
									<p class="form-control-static">
										<?php echo $inventario_estado->getTrabajador_nombre()
												? htmlspecialchars($inventario_estado->getTrabajador_cedula() . ' - ' . $inventario_estado->getTrabajador_nombre())
												: '-- No asignado --'; ?>
									</p>
								</div>

								<!-- Confirmation Modal -->
								<div class="modal fade" id="modal-confirm-sacar" tabindex="-1" aria-labelledby="modalConfirmSacarLabel" aria-hidden="true">
									<div class="modal-dialog">
										<div class="modal-content">
											<div class="modal-header">
												<h5 class="modal-title" id="modalConfirmSacarLabel">Confirmar acción</h5>
												<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
											</div>
											<div class="modal-body">
												¿Está seguro de sacar este activo del contenedor?
												<div class="small text-muted mt-2">
													Esta acción eliminará la asociación del activo con el contenedor actual y registrará el movimiento como salida.
												</div>
												<div id="sacarError" class="text-danger d-none mt-2"></div>
											</div>
											<div class="modal-footer">
												<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
												<button type="button" class="btn btn-danger" id="confirmarSacarBtn">
													<i class="fa fa-check me-1"></i> Confirmar

														<span class="spinner-border spinner-border-sm me-1 d-none" id="spinnerSacar" role="status" aria-hidden="true"></span>

														<!-- spinner will be toggled by JS -->

												</button>
											</div>
										</div>
									</div>
								</div>

								<!-- Modal: Agregar a Contenedor -->
								<div class="modal fade" id="modal-agregar-contenedor" tabindex="-1" aria-labelledby="modalAgregarLabel" aria-hidden="true">
									<div class="modal-dialog">
										<div class="modal-content">
											<div class="modal-header">
												<h5 class="modal-title" id="modalAgregarLabel">Agregar a Contenedor</h5>
												<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
											</div>
											<div class="modal-body">

												<!-- Hidden form to reuse iinventario backend (Asociar) -->
												<form id="asociarForm" action="iinventario" method="POST" class="d-none">
													<input type="hidden" name="id_contenedor" id="asociar_id_contenedor" value="">
													<input type="hidden" name="activos[]" id="asociar_id_activo" value="<?php echo (int)($activo?->getId() ?? 0); ?>">
													<input type="hidden" name="redirect_url" value="scanactivo?id=<?php echo (int)($activo?->getId() ?? 0); ?>">
												</form>


												<div class="mb-3">
													<label for="select-contenedor" class="form-label">Seleccione un contenedor <span class="text-danger">*</span></label>
													<select id="select-contenedor" class="form-select">
														<option value="">-- Seleccione un contenedor --</option>
														<?php foreach ($contenedores as $contenedor): ?>
															<option value="<?php echo (int)$contenedor->getId(); ?>"><?php echo htmlspecialchars($contenedor->getDescripcion()); ?></option>
														<?php endforeach; ?>
													</select>
													<div id="agregarError" class="text-danger d-none mt-2"></div>
												</div>
											</div>
											<div class="modal-footer">
												<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
												<button type="button" class="btn btn-primary" id="btn-confirmar-agregar">
													<span class="spinner-border spinner-border-sm me-1 d-none" id="spinnerAgregar" role="status" aria-hidden="true"></span>
													<span class="label-text">Confirmar</span>
												</button>
											</div>
										</div>
									</div>
								</div>


							</div>
						</div>

						<!-- Hidden form to reuse sinventario backend -->
						<form id="sacarForm" action="sinventario" method="POST" class="d-none">
							<input type="hidden" name="id_contenedor" id="sacar_id_contenedor" value="<?php echo (int)($inventario_estado?->getId_contenedor() ?? 0); ?>">
							<input type="hidden" name="activos[]" id="sacar_id_activo" value="<?php echo (int)($inventario_estado?->getId_activo() ?? 0); ?>">
							<input type="hidden" name="redirect_url" value="scanactivo?id=<?php echo (int)($activo?->getId() ?? 0); ?>">
						</form>

						<?php
							$can_sacar = !empty($inventario_estado) && (int)$inventario_estado->getEn_contenedor() === 1;
							$can_agregar = empty($inventario_estado);
						?>
						<div class="row">
							<div class="col-12">
								<button type="button" class="btn btn-danger me-2" id="btn-sacar-de-contenedor" data-bs-toggle="modal" data-bs-target="#modal-confirm-sacar" <?php echo $can_sacar ? '' : 'disabled'; ?>>
									<i class="fa fa-minus fa-fw me-1"></i> Sacar de Inventario
								</button>
								<button type="button" class="btn btn-primary" id="btn-agregar-contenedor" data-bs-toggle="modal" data-bs-target="#modal-agregar-contenedor" <?php echo $can_agregar ? '' : 'disabled'; ?>>
									<i class="fa fa-plus fa-fw me-1"></i> Agregar a Contenedor
								</button>
							</div>
						</div>

					<?php else: ?>
						<div class="alert alert-secondary mb-0">
							<i class="fa fa-info-circle me-2"></i> Este activo no tiene un estado de inventario registrado.
						</div>
						<div class="row mt-2">
							<div class="col-12">
								<button type="button" class="btn btn-danger me-2" id="btn-sacar-de-contenedor" data-bs-toggle="modal" data-bs-target="#modal-confirm-sacar" disabled>
									<i class="fa fa-minus fa-fw me-1"></i> Sacar de Inventario
								</button>
								<button type="button" class="btn btn-primary" id="btn-agregar-contenedor" data-bs-toggle="modal" data-bs-target="#modal-agregar-contenedor">
									<i class="fa fa-plus fa-fw me-1"></i> Agregar a Contenedor
								</button>
							</div>
						</div>
					<?php endif; ?>
				</div>


			</div>

		</div>
		<!-- END PANEL body -->
	</div>

	<div class="panel panel-inverse mt-3 no-border-radious">
		<div class="panel-heading no-border-radious">
			<h4 class="panel-title">
				Imágenes del Activo
			</h4>
		</div>
		<!-- BEGIN PANEL body -->
		<div class="panel-body">
			<?php if (empty($imagenes)): ?>
				<div class="alert alert-info">
					<i class="fa fa-info-circle me-2"></i> Este activo no tiene imágenes asociadas.
				</div>
			<?php else: ?>
				<div class="row">
					<?php foreach ($imagenes as $imagen): ?>
						<div class="col-md-3 col-sm-4 col-6 mb-3">
							<div class="image-container text-center">
								<img src="<?php echo RUTA; ?>resources/uploads/activos/<?php echo htmlspecialchars($imagen->getNombreArchivo()); ?>"
								     alt="Imagen de activo" class="img-fluid img-thumbnail" style="max-height: 200px; width: auto;">
								<div class="image-caption text-center mt-2">
									<small class="text-muted"><?php echo htmlspecialchars($imagen->getNombreArchivo()); ?></small>
								</div>
							</div>
						</div>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>
		</div>
		<!-- END PANEL body -->
	</div>
<?php #endregion IMAGES ?>
<?php endif; ?>
</div>
<!-- END #content -->


<!-- BEGIN scroll-top-btn -->
<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var confirmarBtn = document.getElementById('confirmarSacarBtn');
        var sacarForm = document.getElementById('sacarForm');
        var errorDiv = document.getElementById('sacarError');
        if (confirmarBtn && sacarForm) {

				// Spinner + disable on sacar confirm
				if (confirmarBtn && sacarForm) {
					confirmarBtn.addEventListener('click', function() {
						var spinner = document.getElementById('spinnerSacar');
						if (spinner) spinner.classList.remove('d-none');
						confirmarBtn.disabled = true;
					});
				}

            confirmarBtn.addEventListener('click', function() {
                // Basic validation mirroring sinventario requirements
                var contId = document.getElementById('sacar_id_contenedor').value;
                var actId = document.getElementById('sacar_id_activo').value;
                if (!contId || !actId) {
                    if (errorDiv) {
                        errorDiv.textContent = 'No se pudo determinar contenedor o activo.';
                        errorDiv.classList.remove('d-none');
                    }
                    return;
                }
                sacarForm.submit();
            });
        }

        // Handle Agregar a Contenedor
        var btnConfirmarAgregar = document.getElementById('btn-confirmar-agregar');
        var selectContenedor = document.getElementById('select-contenedor');
        var asociarForm = document.getElementById('asociarForm');
        var agregarError = document.getElementById('agregarError');

        if (btnConfirmarAgregar && selectContenedor && asociarForm) {
            btnConfirmarAgregar.addEventListener('click', function() {
                var selected = selectContenedor.value;
                if (!selected) {
                    agregarError.textContent = 'Por favor seleccione un contenedor.';
                    agregarError.classList.remove('d-none');
                    return;
                }

					agregarError.classList.add('d-none');
					document.getElementById('asociar_id_contenedor').value = selected;
					// Spinner feedback
					btnConfirmarAgregar.disabled = true;
					var spinner = document.getElementById('spinnerAgregar');
					if (spinner) spinner.classList.remove('d-none');
					asociarForm.submit();
				});
			}

                agregarError.classList.add('d-none');
                document.getElementById('asociar_id_contenedor').value = selected;
                asociarForm.submit();
            });
        }

    });
</script>
<?php #endregion JS ?>

</body>
</html>
